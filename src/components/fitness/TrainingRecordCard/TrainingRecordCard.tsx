import React, { useState, useCallback } from 'react';
import { Card, CardBody, CardFooter } from '@heroui/react';
import { WorkoutCarousel } from '../WorkoutCarousel/WorkoutCarousel';
import { CarouselItem } from '../../../types/feed.types';
import './TrainingRecordCard.scss';

interface TrainingRecordCardProps {
  // 轮播项目（包含训练记录和用户图像）
  carouselItems: CarouselItem[];
  // 用户上传的图片
  userImages?: string[];
  // 是否显示指示器
  showIndicators?: boolean;
  // 自定义样式类名
  className?: string;
  // 查看更多回调
  onViewMore?: () => void;
  // 轮播切换回调
  onCarouselChange?: (index: number) => void;
}

export const TrainingRecordCard: React.FC<TrainingRecordCardProps> = ({
  carouselItems = [],
  userImages = [],
  showIndicators = true,
  className = '',
  onViewMore,
  onCarouselChange
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  // 合并训练记录和用户图像数据
  const combinedItems: CarouselItem[] = React.useMemo(() => {
    const items = [...carouselItems];
    
    // 添加用户上传的图像作为轮播项
    userImages.forEach((imageUrl, index) => {
      items.push({
        id: `user-image-${index}`,
        type: 'user_image',
        content: {
          image_data: {
            url: imageUrl,
            alt: `用户上传图片 ${index + 1}`,
            caption: '用户分享'
          }
        }
      });
    });

    return items;
  }, [carouselItems, userImages]);

  // 处理轮播切换
  const handleCarouselChange = useCallback((index: number) => {
    setCurrentIndex(index);
    onCarouselChange?.(index);
  }, [onCarouselChange]);

  // 处理查看更多点击
  const handleViewMore = useCallback(() => {
    onViewMore?.();
  }, [onViewMore]);

  // 如果没有任何内容，不渲染组件
  if (combinedItems.length === 0) {
    return null;
  }

  return (
    <div className={`training-record-card-wrapper ${className}`}>
      <Card className="training-record-card" shadow="sm" radius="md">
        <CardBody className="training-record-body p-0">
          <div className="carousel-container">
            <WorkoutCarousel
              items={combinedItems}
              showIndicators={showIndicators && combinedItems.length > 1}
              className="training-record-carousel"
              onItemChange={handleCarouselChange}
            />
            
            {/* 轮播项目类型指示器 */}
            {combinedItems.length > 1 && (
              <div className="item-type-indicator">
                <span className="current-item-info">
                  {currentIndex + 1} / {combinedItems.length}
                  {combinedItems[currentIndex]?.type === 'user_image' ? ' (图片)' : ' (训练)'}
                </span>
              </div>
            )}
          </div>
        </CardBody>
        
        <CardFooter className="training-record-footer">
          <div className="footer-content">
            <button 
              className="view-more-btn"
              onClick={handleViewMore}
            >
              查看更多运动详情
            </button>
            
            {/* 轮播导航按钮 */}
            {combinedItems.length > 1 && (
              <div className="carousel-nav">
                <button
                  className="nav-btn prev-btn"
                  onClick={() => handleCarouselChange(
                    currentIndex > 0 ? currentIndex - 1 : combinedItems.length - 1
                  )}
                  disabled={combinedItems.length <= 1}
                >
                  ‹
                </button>
                <button
                  className="nav-btn next-btn"
                  onClick={() => handleCarouselChange(
                    currentIndex < combinedItems.length - 1 ? currentIndex + 1 : 0
                  )}
                  disabled={combinedItems.length <= 1}
                >
                  ›
                </button>
              </div>
            )}
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default TrainingRecordCard; 